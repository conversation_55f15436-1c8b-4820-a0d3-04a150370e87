import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { MindMapData, NodeData, ChatMessage, ViewportState } from '../types';
import { DEFAULT_MODEL } from '../config/models';
import { useCanvasManager } from './useCanvasManager';
import { safeNodeUpdate, logNodeDataChange } from '../utils/nodeDataValidation';

const API_KEY_STORAGE_KEY = 'gemini-api-key';
const SELECTED_MODEL_STORAGE_KEY = 'gemini-selected-model';

const createInitialNode = (x: number = 400, y: number = 300, searchGrounding: boolean = false): NodeData => ({
  id: uuidv4(),
  x,
  y,
  title: 'Main Topic',
  query: undefined,
  response: undefined,
  sources: undefined,
  messages: [],
  isExpanded: true,
  childIds: [],
  isSelected: false,
  width: 450,
  height: 200,
  searchGrounding,
  hasQueried: false,
});

const createInitialData = (): MindMapData => {
  const rootNode = createInitialNode();
  return {
    nodes: { [rootNode.id]: rootNode },
    rootNodeId: rootNode.id,
    searchGrounding: false,
    selectedModel: DEFAULT_MODEL.id,
    version: 1,
    lastModified: Date.now(),
  };
};

export const useMindMap = () => {
  const canvasManager = useCanvasManager();

  // Use active canvas data and viewport directly from canvas manager
  const data = canvasManager.activeCanvas?.data;
  const viewport = canvasManager.activeCanvas?.viewport || { x: 0, y: 0, zoom: 1 };

  // Always ensure we have valid data - use fallback only when absolutely necessary
  const fallbackData = data || createInitialData();



  const [apiKey, setApiKey] = useState<string>(() => {
    return localStorage.getItem(API_KEY_STORAGE_KEY) || '';
  });

  const [selectedModel, setSelectedModel] = useState<string>(() => {
    return localStorage.getItem(SELECTED_MODEL_STORAGE_KEY) || DEFAULT_MODEL.id;
  });

  // Update canvas data when it changes
  const setData = useCallback((newData: MindMapData | ((prev: MindMapData) => MindMapData)) => {
    if (!canvasManager.activeCanvas || !canvasManager.activeCanvas.data) return;

    const currentData = canvasManager.activeCanvas.data;
    const updatedData = typeof newData === 'function' ? newData(currentData) : newData;
    canvasManager.updateCanvas(canvasManager.activeCanvasId, {
      data: { ...updatedData, lastModified: Date.now() }
    });
  }, [canvasManager]);

  // Update viewport
  const setViewport = useCallback((newViewport: ViewportState | ((prev: ViewportState) => ViewportState)) => {
    if (!canvasManager.activeCanvas) return;

    const updatedViewport = typeof newViewport === 'function' ? newViewport(canvasManager.activeCanvas.viewport) : newViewport;
    canvasManager.updateCanvas(canvasManager.activeCanvasId, { viewport: updatedViewport });
  }, [canvasManager]);

  useEffect(() => {
    if (apiKey) {
      localStorage.setItem(API_KEY_STORAGE_KEY, apiKey);
    } else {
      localStorage.removeItem(API_KEY_STORAGE_KEY);
    }
  }, [apiKey]);

  useEffect(() => {
    localStorage.setItem(SELECTED_MODEL_STORAGE_KEY, selectedModel);
  }, [selectedModel]);

  const updateNode = useCallback((nodeId: string, updates: Partial<NodeData>) => {
    setData(prev => {
      if (!prev) {
        console.warn('updateNode: No data available in setData callback');
        return prev;
      }

      // Ensure we have the latest node data to prevent race conditions
      const currentNode = prev.nodes[nodeId];
      if (!currentNode) {
        console.warn(`Attempted to update non-existent node: ${nodeId}`);
        return prev;
      }

      try {
        // Use safe update to preserve data integrity
        const updatedNode = safeNodeUpdate(currentNode, updates);

        // Log data changes for debugging
        logNodeDataChange(nodeId, currentNode, updatedNode, 'updateNode');

        return {
          ...prev,
          nodes: {
            ...prev.nodes,
            [nodeId]: updatedNode,
          },
        };
      } catch (error) {
        console.error(`Failed to safely update node ${nodeId}:`, error);
        return prev; // Return unchanged state if update fails
      }
    });
  }, [setData]);

  const addMessage = useCallback((nodeId: string, message: ChatMessage) => {
    setData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        nodes: {
          ...prev.nodes,
          [nodeId]: {
            ...prev.nodes[nodeId],
            messages: [...prev.nodes[nodeId].messages, message],
          },
        },
      };
    });
  }, [setData]);

  const setNodeQuery = useCallback((nodeId: string, query: string, response: string, sources?: Array<{title: string; uri: string}>) => {
    setData(prev => {
      if (!prev) return prev;
      const currentNode = prev.nodes[nodeId];
      if (!currentNode) {
        console.warn(`Attempted to set query for non-existent node: ${nodeId}`);
        return prev;
      }

      return {
        ...prev,
        nodes: {
          ...prev.nodes,
          [nodeId]: {
            ...currentNode, // Preserve all existing node data
            query,
            response,
            sources,
            hasQueried: true,
          },
        },
      };
    });
  }, [setData]);

  const clearNodeQuery = useCallback((nodeId: string) => {
    setData(prev => {
      if (!prev) return prev;
      const currentNode = prev.nodes[nodeId];
      if (!currentNode) {
        console.warn(`Attempted to clear query for non-existent node: ${nodeId}`);
        return prev;
      }

      return {
        ...prev,
        nodes: {
          ...prev.nodes,
          [nodeId]: {
            ...currentNode, // Preserve all existing node data
            // Keep the current query but clear the response and sources
            response: undefined,
            sources: undefined,
            hasQueried: false,
          },
        },
      };
    });
  }, [setData]);

  const createChildNode = useCallback((parentId: string) => {
    let newNodeId: string | undefined;

    setData(prev => {
      if (!prev) return prev;
      const parent = prev.nodes[parentId];
      if (!parent) return prev;

      const newNode: NodeData = {
        id: uuidv4(),
        x: parent.x + parent.width + 50, // Position to the right of the parent with a margin
        y: parent.y + parent.childIds.length * 250,
        title: 'New Topic',
        query: undefined,
        response: undefined,
        sources: undefined,
        messages: [],
        isExpanded: true,
        parentId,
        childIds: [],
        isSelected: false,
        width: 450,
        height: 200,
        searchGrounding: prev.searchGrounding, // Use global default for new nodes
        hasQueried: false,
      };

      newNodeId = newNode.id;

      return {
        ...prev,
        nodes: {
          ...prev.nodes,
          [newNode.id]: newNode,
          [parentId]: {
            ...prev.nodes[parentId],
            childIds: [...prev.nodes[parentId].childIds, newNode.id],
          },
        },
      };
    });

    return newNodeId;
  }, [setData]);

  const deleteNode = useCallback((nodeId: string) => {
    setData(prev => {
      if (!prev) return prev;
      const node = prev.nodes[nodeId];
      if (!node || nodeId === prev.rootNodeId) return prev;

      const newNodes = { ...prev.nodes };

      // Remove from parent's children
      if (node.parentId) {
        const parent = newNodes[node.parentId];
        if (parent) {
          newNodes[node.parentId] = {
            ...parent,
            childIds: parent.childIds.filter(id => id !== nodeId),
          };
        }
      }

      // Delete node and all its children recursively
      const deleteRecursively = (id: string) => {
        const nodeToDelete = newNodes[id];
        if (nodeToDelete) {
          nodeToDelete.childIds.forEach(deleteRecursively);
          delete newNodes[id];
        }
      };

      deleteRecursively(nodeId);

      return { ...prev, nodes: newNodes };
    });
  }, [setData]);

  const toggleSearchGrounding = useCallback(() => {
    setData(prev => {
      if (!prev) return prev;
      return { ...prev, searchGrounding: !prev.searchGrounding };
    });
  }, [setData]);

  const toggleNodeSearchGrounding = useCallback((nodeId: string) => {
    setData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        nodes: {
          ...prev.nodes,
          [nodeId]: {
            ...prev.nodes[nodeId],
            searchGrounding: !prev.nodes[nodeId].searchGrounding,
          },
        },
      };
    });
  }, [setData]);

  const clearNodeChat = useCallback((nodeId: string) => {
    updateNode(nodeId, { messages: [] });
  }, [updateNode]);

  const selectNode = useCallback((nodeId: string) => {
    setData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        nodes: Object.fromEntries(
          Object.entries(prev.nodes).map(([id, node]) => [
            id,
            { ...node, isSelected: id === nodeId },
          ])
        ),
      };
    });
  }, [setData]);

  const exportData = useCallback(() => {
    const currentData = canvasManager.activeCanvas?.data;
    if (!currentData) return;
    const blob = new Blob([JSON.stringify(currentData, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mindmap-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [canvasManager]);

  const importData = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        // Validate the imported data structure
        if (imported.nodes && imported.rootNodeId && imported.nodes[imported.rootNodeId]) {
          setData(imported);
          setViewport({ x: 0, y: 0, zoom: 1 }); // Reset viewport when importing
        } else {
          console.error('Invalid mind map file format');
          alert('Invalid mind map file format. Please select a valid mind map export file.');
        }
      } catch (error) {
        console.error('Failed to import data:', error);
        alert('Failed to import mind map. Please check the file format.');
      }
    };
    reader.readAsText(file);
  }, [setData, setViewport]);

  return {
    data: data || fallbackData, // Use live data from canvas manager, fallback only if null
    viewport,
    setViewport,
    updateNode,
    addMessage,
    setNodeQuery,
    clearNodeQuery,
    createChildNode,
    deleteNode,
    toggleSearchGrounding,
    toggleNodeSearchGrounding,
    clearNodeChat,
    selectNode,
    exportData,
    importData,
    apiKey,
    setApiKey,
    selectedModel,
    setSelectedModel,
    // Canvas management
    canvasManager,
  };
};