import { useRef, useCallback } from 'react';
import { dragPerformanceMonitor } from '../utils/performance';

// Throttle utility for performance optimization
const throttle = <T extends (...args: any[]) => void>(func: T, delay: number): T => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;
  
  return ((...args: any[]) => {
    const currentTime = Date.now();
    
    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  }) as T;
};

interface DragState {
  isDragging: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
  offsetX: number;
  offsetY: number;
}

interface UseDragOptimizationProps {
  initialX: number;
  initialY: number;
  onUpdate: (x: number, y: number) => void;
  onDragStart?: () => void;
  onDragEnd?: () => void;
  throttleDelay?: number;
}

export const useDragOptimization = ({
  initialX,
  initialY,
  onUpdate,
  onDragStart,
  onDragEnd,
  throttleDelay = 16 // ~60fps
}: UseDragOptimizationProps) => {
  const dragStateRef = useRef<DragState>({
    isDragging: false,
    startX: 0,
    startY: 0,
    currentX: initialX,
    currentY: initialY,
    offsetX: 0,
    offsetY: 0
  });

  const elementRef = useRef<HTMLElement>(null);

  // Throttled update function for better performance
  const throttledUpdate = useRef(
    throttle((x: number, y: number) => {
      // Validate coordinates before updating
      if (typeof x === 'number' && typeof y === 'number' &&
          !isNaN(x) && !isNaN(y)) {
        onUpdate(x, y);
      }
    }, throttleDelay)
  );

  const startDrag = useCallback((e: React.MouseEvent | MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Update drag state
    dragStateRef.current = {
      isDragging: true,
      startX: e.clientX,
      startY: e.clientY,
      currentX: initialX,
      currentY: initialY,
      offsetX: e.clientX - initialX,
      offsetY: e.clientY - initialY
    };

    // Start performance monitoring
    dragPerformanceMonitor.start();

    onDragStart?.();

    // Prevent text selection during drag
    document.body.style.userSelect = 'none';
  }, [initialX, initialY, onDragStart]);

  const updateDrag = useCallback((e: MouseEvent) => {
    if (!dragStateRef.current.isDragging) return;

    e.preventDefault();

    // Record frame for performance monitoring
    dragPerformanceMonitor.recordFrame();

    // Calculate new position
    const newX = e.clientX - dragStateRef.current.offsetX;
    const newY = e.clientY - dragStateRef.current.offsetY;

    // Update current position in ref
    dragStateRef.current.currentX = newX;
    dragStateRef.current.currentY = newY;

    // Apply immediate visual feedback using transform
    if (elementRef.current) {
      const deltaX = newX - initialX;
      const deltaY = newY - initialY;
      // Apply translation without affecting the scale set by CSS
      elementRef.current.style.transform = `translate(${deltaX}px, ${deltaY}px) scale(1.02)`;
    }

    // Throttled state update for performance
    throttledUpdate.current(newX, newY);
  }, [initialX, initialY]);

  const endDrag = useCallback(() => {
    if (!dragStateRef.current.isDragging) return;

    // Stop performance monitoring and log results
    const metrics = dragPerformanceMonitor.stop();
    if (process.env.NODE_ENV === 'development') {
      console.log('Drag Performance Metrics:', {
        averageFPS: Math.round(metrics.averageFPS),
        frameCount: metrics.frameCount,
        totalTime: Math.round(metrics.totalTime),
        minFrameTime: Math.round(metrics.minFrameTime * 100) / 100,
        maxFrameTime: Math.round(metrics.maxFrameTime * 100) / 100
      });
    }

    // Final position update - ensure we have valid coordinates
    const finalX = dragStateRef.current.currentX;
    const finalY = dragStateRef.current.currentY;

    if (typeof finalX === 'number' && typeof finalY === 'number' &&
        !isNaN(finalX) && !isNaN(finalY)) {
      onUpdate(finalX, finalY);
    } else {
      console.warn('Invalid drag coordinates detected, skipping final update:', { finalX, finalY });
    }

    // Reset drag state
    dragStateRef.current.isDragging = false;
    document.body.style.userSelect = '';

    // Reset transform
    if (elementRef.current) {
      elementRef.current.style.transform = '';
    }

    onDragEnd?.();
  }, [onUpdate, onDragEnd]);

  const isDragging = dragStateRef.current.isDragging;

  return {
    elementRef,
    isDragging,
    startDrag,
    updateDrag,
    endDrag,
    dragState: dragStateRef.current
  };
};
