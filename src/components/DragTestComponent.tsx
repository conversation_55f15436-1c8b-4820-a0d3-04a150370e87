import React, { useState } from 'react';
import { useMindMap } from '../hooks/useMindMap';

/**
 * Test component to verify drag functionality and data persistence
 */
export const DragTestComponent: React.FC = () => {
  const { data, updateNode, setNodeQuery, createChildNode } = useMindMap();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testDragDataPersistence = async () => {
    if (!data) {
      addTestResult('❌ No data available');
      return;
    }

    try {
      // Create a child node
      createChildNode(data.rootNodeId);
      addTestResult('✅ Created child node');

      // Find the child node
      const nodeIds = Object.keys(data.nodes);
      const childNodeId = nodeIds.find(id => id !== data.rootNodeId);
      
      if (!childNodeId) {
        addTestResult('❌ Child node not found');
        return;
      }

      // Add query data to the child node
      const testQuery = 'Test drag persistence';
      const testResponse = 'This is a test response to verify data persistence during drag operations.';
      setNodeQuery(childNodeId, testQuery, testResponse);
      addTestResult('✅ Added query data to child node');

      // Wait a bit for state to update
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify data was set
      const nodeWithData = data.nodes[childNodeId];
      if (nodeWithData?.query === testQuery && nodeWithData?.response === testResponse) {
        addTestResult('✅ Query data verified');
      } else {
        addTestResult('❌ Query data not set properly');
        return;
      }

      // Simulate drag operation by updating position multiple times
      const positions = [
        { x: 100, y: 100 },
        { x: 150, y: 120 },
        { x: 200, y: 140 },
        { x: 250, y: 160 },
        { x: 300, y: 180 },
      ];

      for (const { x, y } of positions) {
        updateNode(childNodeId, { x, y });
        await new Promise(resolve => setTimeout(resolve, 50)); // Simulate drag timing
      }

      addTestResult('✅ Completed position updates');

      // Verify data persistence after drag
      const finalNode = data.nodes[childNodeId];
      if (finalNode?.query === testQuery && 
          finalNode?.response === testResponse &&
          finalNode?.x === 300 && 
          finalNode?.y === 180) {
        addTestResult('✅ Data persisted through drag operations!');
      } else {
        addTestResult(`❌ Data lost during drag: query=${!!finalNode?.query}, response=${!!finalNode?.response}, x=${finalNode?.x}, y=${finalNode?.y}`);
      }

    } catch (error) {
      addTestResult(`❌ Test failed with error: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="fixed top-4 right-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg p-4 shadow-lg z-50 max-w-md">
      <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
        Drag Test Component
      </h3>
      
      <div className="space-y-2 mb-4">
        <button
          onClick={testDragDataPersistence}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
        >
          Test Drag Data Persistence
        </button>
        
        <button
          onClick={clearResults}
          className="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
        >
          Clear Results
        </button>
      </div>

      <div className="max-h-64 overflow-y-auto">
        <h4 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
          Test Results:
        </h4>
        <div className="space-y-1">
          {testResults.length === 0 ? (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              No tests run yet
            </p>
          ) : (
            testResults.map((result, index) => (
              <div
                key={index}
                className={`text-xs p-2 rounded ${
                  result.includes('✅')
                    ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                    : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                }`}
              >
                {result}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
