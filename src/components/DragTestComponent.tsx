import React, { useState } from 'react';
import { useMindMap } from '../hooks/useMindMap';

/**
 * Test component to verify drag functionality and data persistence
 */
export const DragTestComponent: React.FC = () => {
  const mindMapHook = useMindMap();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testDragDataPersistence = async () => {
    // Get fresh data reference
    const getCurrentData = () => mindMapHook.data;

    if (!getCurrentData()) {
      addTestResult('❌ No data available');
      return;
    }

    try {
      // Create a child node
      mindMapHook.createChildNode(getCurrentData()!.rootNodeId);
      addTestResult('✅ Created child node');

      // Wait for state update
      await new Promise(resolve => setTimeout(resolve, 200));

      // Find the child node with fresh data
      let currentData = getCurrentData();
      const nodeIds = Object.keys(currentData?.nodes || {});
      const childNodeId = nodeIds.find(id => id !== currentData?.rootNodeId);

      if (!childNodeId) {
        addTestResult('❌ Child node not found');
        return;
      }

      // Log initial position
      const initialNode = currentData?.nodes[childNodeId];
      addTestResult(`🔍 Initial position: (${initialNode?.x}, ${initialNode?.y})`);

      // Add query data to the child node
      const testQuery = 'Test drag persistence';
      const testResponse = 'This is a test response to verify data persistence during drag operations.';
      mindMapHook.setNodeQuery(childNodeId, testQuery, testResponse);
      addTestResult('✅ Added query data to child node');

      // Wait for state to update
      await new Promise(resolve => setTimeout(resolve, 200));

      // Verify data was set with fresh data
      currentData = getCurrentData();
      const nodeWithData = currentData?.nodes[childNodeId];
      if (nodeWithData?.query === testQuery && nodeWithData?.response === testResponse) {
        addTestResult('✅ Query data verified');
      } else {
        addTestResult('❌ Query data not set properly');
        return;
      }

      // Simulate drag operation by updating position multiple times
      const positions = [
        { x: 100, y: 100 },
        { x: 150, y: 120 },
        { x: 200, y: 140 },
        { x: 250, y: 160 },
        { x: 300, y: 180 },
      ];

      for (let i = 0; i < positions.length; i++) {
        const { x, y } = positions[i];

        // Log before update
        const beforeData = getCurrentData();
        const beforeNode = beforeData?.nodes[childNodeId];
        addTestResult(`🔄 Before update ${i + 1}: (${beforeNode?.x},${beforeNode?.y})`);

        mindMapHook.updateNode(childNodeId, { x, y });

        // Check immediately after (might not be updated yet)
        const immediateData = getCurrentData();
        const immediateNode = immediateData?.nodes[childNodeId];
        addTestResult(`⚡ Immediate after ${i + 1}: Set (${x},${y}) -> Got (${immediateNode?.x},${immediateNode?.y})`);

        await new Promise(resolve => setTimeout(resolve, 200)); // Longer wait for state updates

        // Check after delay
        const currentData = getCurrentData();
        const currentNode = currentData?.nodes[childNodeId];
        addTestResult(`✅ After delay ${i + 1}: Set (${x},${y}) -> Got (${currentNode?.x},${currentNode?.y})`);
      }

      addTestResult('✅ Completed position updates');

      // Wait for final state update
      await new Promise(resolve => setTimeout(resolve, 200));

      // Verify data persistence after drag with fresh data
      currentData = getCurrentData();
      const finalNode = currentData?.nodes[childNodeId];

      if (finalNode?.query === testQuery &&
          finalNode?.response === testResponse &&
          finalNode?.x === 300 &&
          finalNode?.y === 180) {
        addTestResult('✅ Data persisted through drag operations!');
      } else {
        addTestResult(`❌ Position mismatch: query=${!!finalNode?.query}, response=${!!finalNode?.response}, expected=(300,180), actual=(${finalNode?.x},${finalNode?.y})`);

        // Additional debugging info
        addTestResult(`🔍 Debug: Node title="${finalNode?.title}", hasQueried=${finalNode?.hasQueried}`);
      }

    } catch (error) {
      addTestResult(`❌ Test failed with error: ${error}`);
    }
  };

  const testSimplePositionUpdate = async () => {
    const getCurrentData = () => mindMapHook.data;

    if (!getCurrentData()) {
      addTestResult('❌ No data available');
      return;
    }

    try {
      // Find the root node
      const rootNodeId = getCurrentData()!.rootNodeId;
      const rootNode = getCurrentData()!.nodes[rootNodeId];

      addTestResult(`🔍 Root node initial position: (${rootNode.x}, ${rootNode.y})`);

      // Test a simple position update on the root node
      const newX = 123;
      const newY = 456;

      mindMapHook.updateNode(rootNodeId, { x: newX, y: newY });
      addTestResult(`🔄 Updated root node to (${newX}, ${newY})`);

      // Wait for state update
      await new Promise(resolve => setTimeout(resolve, 200));

      // Check the result
      const currentData = getCurrentData();
      const updatedNode = currentData?.nodes[rootNodeId];

      if (updatedNode?.x === newX && updatedNode?.y === newY) {
        addTestResult(`✅ Simple position update successful: (${updatedNode.x}, ${updatedNode.y})`);
      } else {
        addTestResult(`❌ Simple position update failed: expected (${newX}, ${newY}), got (${updatedNode?.x}, ${updatedNode?.y})`);
      }

    } catch (error) {
      addTestResult(`❌ Simple test failed with error: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="fixed top-4 right-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg p-4 shadow-lg z-50 max-w-md">
      <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
        Drag Test Component
      </h3>
      
      <div className="space-y-2 mb-4">
        <button
          onClick={testDragDataPersistence}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
        >
          Test Drag Data Persistence
        </button>

        <button
          onClick={testSimplePositionUpdate}
          className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors"
        >
          Test Simple Position Update
        </button>
        
        <button
          onClick={clearResults}
          className="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
        >
          Clear Results
        </button>
      </div>

      <div className="max-h-64 overflow-y-auto">
        <h4 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
          Test Results:
        </h4>
        <div className="space-y-1">
          {testResults.length === 0 ? (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              No tests run yet
            </p>
          ) : (
            testResults.map((result, index) => (
              <div
                key={index}
                className={`text-xs p-2 rounded ${
                  result.includes('✅')
                    ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                    : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                }`}
              >
                {result}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
